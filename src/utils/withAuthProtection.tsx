import React, { useEffect } from "react";
import { useAuth } from "../context/AuthContext";
import { useNavigate } from "react-router-dom";
import Loader from "../components/common/loader";

const withAuthProtection = <P extends object>(
  WrappedComponent: React.ComponentType<P>
): React.FC<P> => {
  const WithAuthProtection: React.FC<P> = (props) => {
    const { userInfo, authLoading, isAuth } = useAuth();
    const navigate = useNavigate();

    useEffect(() => {
      // Only redirect if auth loading is complete and user is not authenticated
      if (!authLoading && !isAuth) {
        navigate("/login", { replace: true });
      }
    }, [authLoading, isAuth, navigate]);

    // Show loader while checking authentication
    if (authLoading) {
      return <Loader />;
    }

    // If not authenticated, show loader briefly before redirect
    if (!isAuth) {
      return <Loader />;
    }

    // User is authenticated, render the protected component
    return <WrappedComponent {...props} />;
  };

  // Add display name for better debugging
  // WithAuthProtection.displayName = `withAuthProtection(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;

  return WithAuthProtection;
};

export default withAuthProtection;
