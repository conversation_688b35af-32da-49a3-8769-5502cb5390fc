import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.tsx";
import { AllCommunityModule, ModuleRegistry } from "ag-grid-community";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { AuthProvider } from "./context/AuthContext.tsx";
import { ApiDataProvider } from "./context/ApiContext.tsx";

ModuleRegistry.registerModules([AllCommunityModule]);

createRoot(document.getElementById("root")!).render(
  <AuthProvider>
    <ApiDataProvider>
      <App />
    </ApiDataProvider>
  </AuthProvider>
);
