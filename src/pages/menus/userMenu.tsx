import { Link, useParams } from "react-router-dom";
import { useApi } from "../../context/ApiContext";
import { useAuth } from "../../context/AuthContext";
import InputField from "../../components/common/InputField";
import { useForm, type SubmitHandler } from "react-hook-form";
import axios from "axios";
import { getApiUrl } from "../../utils/api";
import { useEffect, useState } from "react";
import Loader from "../../components/common/loader";

interface InputSearch {
  search: string;
}

interface Outlet {
  name: string;
  slug: string;
}

interface Category {
  name: string;
  slug: string;
  image: string;
}

interface MenuItem {
  name: string;
  slug: string;
  image: string;
  price: number;
  is_available?: boolean;
}

const UserMenu = () => {
  const { userSlug, categories } = useApi() as {
    userSlug: Outlet | null;
    categories: Category[];
  };

  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [searchResults, setSearchResults] = useState<MenuItem[]>([]);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);

  const { slug } = useParams<{ slug: string }>();
  const [categorySlug, setCategorySlug] = useState(slug);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<InputSearch>();

  const changeSlugInUrl = (newSlug: string | undefined) => {
    window.history.replaceState(null, "", `/menu/user-menu/${newSlug}`);
    setCategorySlug(newSlug);
  };

  const getMenuItems = async () => {
    try {
      setLoading(true);
      const res = await axios.get(
        getApiUrl(`/api/open/menu/${userSlug?.slug}/${categorySlug}`)
      );
      setMenuItems(res?.data?.data?.items || []);
    } catch (error) {
      console.error("Error fetching menu items:", error);
      setMenuItems([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch: SubmitHandler<InputSearch> = async (data) => {
    try {
      setSearchLoading(true);
      const res = await axios.get(
        getApiUrl(
          `/api/open/menu/${userSlug?.slug}/search/contextual?query=${data?.search}`
        )
      );

      const slugArray: string[] = res?.data?.data?.items || [];
      const matchedItems = menuItems.filter((item) =>
        slugArray.includes(item.slug)
      );
      setSearchResults(matchedItems);
      setIsSearchActive(true);
    } catch (e) {
      console.log({ e });
    } finally {
      setSearchLoading(false);
    }
  };

  const handleClearSearch = () => {
    setSearchResults([]);
    setIsSearchActive(false);
    reset();
  };

  useEffect(() => {
    if (userSlug) {
      getMenuItems();
    }
  }, [userSlug?.slug, categorySlug]);

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = "/food.png"; // fallback image
  };

  if (loading || searchLoading) return <Loader />;

  const itemsToShow = isSearchActive ? searchResults : menuItems;

  return (
    <section className="relative min-h-[200px]">
      <h1 className="my-4 text-base font-semibold tracking-[1px] text-[#111719] uppercase">
        Whatever You Need
      </h1>

      {/* Search Form */}
      <form onSubmit={handleSubmit(handleSearch)}>
        <div className="flex items-center gap-2">
          <InputField
            id="search"
            type="text"
            placeholder="Ask AI..."
            className="bg-white"
            {...register("search")}
            error={errors.search?.message}
          />
          <button
            type="submit"
            className="px-4 py-2 text-white rounded-md cursor-pointer"
            style={{ backgroundColor: "var(--color-orange)" }}
          >
            Search
          </button>
          {isSearchActive && (
            <button
              type="button"
              onClick={handleClearSearch}
              className="px-4 py-2 rounded-md text-[#111719] cursor-pointer"
              style={{
                backgroundColor: "var(--color-orange-light)",
                border: "1px solid var(--color-orange-border)",
              }}
            >
              Clear
            </button>
          )}
        </div>
      </form>

      {/* Categories Scrollbar */}
      <ul className="flex gap-3 w-[calc(100vw-24px)] mt-3 overflow-x-auto overflow-y-hidden no-scrollbar py-2 px-1">
        {categories?.map((category) => {
          const isActive = categorySlug === category.slug;
          return (
            <li
              key={category.slug}
              className={`flex-shrink-0 p-2 rounded-full font-medium text-sm cursor-pointer transition ${
                isActive
                  ? "bg-[#8bc652] text-white shadow-[0px_1px_3px_0px_#0000001A]"
                  : "bg-white text-[#111719] shadow-sm hover:shadow-md"
              }`}
            >
              <button
                onClick={() => changeSlugInUrl(category.slug)}
                className="w-full h-full flex items-center gap-2"
              >
                <span className="w-9 h-9 border border-white rounded-full">
                  <img
                    src={category.image}
                    alt={category.name}
                    onError={handleImageError}
                    className="rounded-full"
                  />
                </span>
                <span className="text-sm">{category.name}</span>
              </button>
            </li>
          );
        })}
      </ul>

      {/* No Items Fallback */}
      {itemsToShow?.length === 0 && (
        <p className="text-gray-500 mt-6 text-center">No items found.</p>
      )}

      {/* Items Grid */}
      <ul className="mt-6 grid grid-cols-1 sm:grid-cols-2 gap-4">
        {itemsToShow.map((item) => {
          return (
            item?.is_available && (
              <li
                key={item.slug}
                className="bg-white rounded-2xl overflow-hidden shadow-md border border-[#F1F1F1] hover:shadow-lg transition-shadow"
              >
                <div className="relative">
                  <img
                    src={`https://dev.dishto.in${item.image}`}
                    alt={item.name}
                    className="w-full h-auto object-cover"
                    onError={handleImageError}
                  />
                </div>
                <div className="p-4 flex justify-between items-center">
                  <h3 className="text-base font-semibold text-[#111719]">
                    {item.name}
                  </h3>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-orange-600 font-medium">
                      ₹{Number(item.price).toFixed(2)}
                    </span>
                  </div>
                </div>
              </li>
            )
          );
        })}
      </ul>
    </section>
  );
};

export default UserMenu;
