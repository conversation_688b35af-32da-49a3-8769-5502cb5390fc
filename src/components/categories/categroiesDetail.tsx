import { useParams } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { useEffect, useState } from "react";
import axios from "axios";
import { getApiUrl } from "../../utils/api";
import { closestCenter, DndContext, type DragEndEvent } from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import SortableItem from "../common/SortableItem";

const CategroiesDetail = () => {
  const { slug } = useParams();
  const { outletSlug, setLoading, loading } = useAuth();
  const [sortLoading, setSortLoading] = useState(false);
  const [userMneuItems, setUserMenuItems] = useState([]);
  const getMenuItems = async () => {
    try {
      setLoading(true);
      //   setError(null);
      // console.log("Called");
      const res = await axios.get(
        // getApiUrl(
        //   `/api/protected/restaurant/420a35a2a102426f_1749822804014/items?category_slug=${slug}&slug=__all__`
        // ),
        getApiUrl(
          `/api/protected/restaurant/${outletSlug?.slug}/items?category_slug=${slug}&slug=__all__`
        ),
        {
          withCredentials: true,
        }
      );

      // console.log({ res });

      // Set the menu items directly from API
      setUserMenuItems(res?.data?.data?.items);
    } catch (error) {
      console.error("Error fetching menu items:", error);
      //   setError("Failed to load menu items");
      // Fallback to empty array on error
      //   setMenuItems([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (outletSlug?.slug) {
      getMenuItems();
    }
  }, [slug, outletSlug?.slug]);

  // console.log({ userMneuItems });

  if (loading) return <h1>loading....</h1>;
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) return;

    const oldIndex = userMneuItems.findIndex(
      (item) => item?.slug === active.id
    );
    const newIndex = userMneuItems.findIndex((item) => item?.slug === over.id);

    const newSorted = arrayMove(userMneuItems, oldIndex, newIndex);
    setUserMenuItems(newSorted);

    try {
      setSortLoading(true);
      await axios.post(
        getApiUrl(
          `/api/protected/restaurant/${outletSlug?.slug}/items/${slug}/rearrange_display_order`
        ),
        {
          ordering: newSorted.map((item, index) => ({
            menu_item_slug: item?.slug,
            display_order: index + 1,
          })),
        },
        { withCredentials: true }
      );
      //   toast.success("Order updated successfully");
    } catch (error) {
      console.error("Error updating order:", error);
      //   toast.error("Failed to update order");
      getMenuItems(); // Re-fetch to restore original order
    } finally {
      setSortLoading(false);
    }
  };
  return (
    <div className="relative">
      <h1 className="text-xl font-bold mb-4">Category: {slug}</h1>

      {sortLoading && (
        <div
          className="absolute inset-0 bg-opacity-30 flex items-center justify-center z-50 rounded-lg"
          style={{ backgroundColor: "rgba(0, 0, 0, 0.3)" }}
        >
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange mx-auto mb-2"></div>
            <p className="text-white text-sm">Updating order...</p>
          </div>
        </div>
      )}

      <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <SortableContext
          items={userMneuItems.map((item) => item?.slug)}
          strategy={verticalListSortingStrategy}
        >
          {userMneuItems.map((item) => (
            <SortableItem key={item?.slug} id={item?.slug}>
              <div className="flex items-center gap-4  transition-all">
                <div>
                  <img
                    src={`https://dev.dishto.in${item?.image}`} // Replace with actual image field
                    alt={item?.name}
                    className="w-24 h-24 object-cover rounded-xl"
                  />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">
                    {item?.name}
                  </h3>
                  <span className="text-sm font-semibold text-[#E67E22]">
                    ₹{Number(item?.price).toFixed(2)}
                  </span>
                  {/* Optional: Add description or price */}
                  {/* <p className="text-sm text-gray-500">{item.description}</p> */}
                </div>
              </div>
            </SortableItem>
          ))}
        </SortableContext>
      </DndContext>
    </div>
  );
};

export default CategroiesDetail;
