import React, { useEffect, useState } from "react";
import axios from "axios";
import withAuthProtection from "../../utils/withAuthProtection";
import Icon from "../common/Icon";
import { getApiUrl } from "../../utils/api";
import InputField from "../common/InputField";
import CustomButton from "../common/CustomButton";
import CategoryDataTable from "../common/categoryDataTable";
import MobileCategory from "../common/mobileCategory";
import toast from "react-hot-toast";
import { useAuth } from "../../context/AuthContext";
import { useApi } from "../../context/ApiContext";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import SortableItem from "../common/SortableItem";
import { closestCenter, DndContext } from "@dnd-kit/core";
import { Link } from "react-router-dom";

// Interface for category data from API
interface Category {
  id: string;
  name: string;
  description: string;
  outlet: string;
  slug?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

const CategoriesListPage: React.FC = () => {
  const { outletSlug } = useAuth();
  const { categories, getCategories } = useApi();
  const [sortLoading, setSortLoading] = useState(false);
  const [sortedCategories, setSortedCategories] = useState(categories || []);
  const [error, setError] = useState<string | null>(null);

  // Update local state when categories change
  useEffect(() => {
    setSortedCategories(categories || []);
  }, [categories]);

  const handleDragEnd = async (event) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = sortedCategories.findIndex(
        (cat) => cat.slug === active.id
      );
      const newIndex = sortedCategories.findIndex(
        (cat) => cat.slug === over.id
      );

      const newSortedCategories = arrayMove(
        sortedCategories,
        oldIndex,
        newIndex
      );

      // Update UI
      setSortedCategories(newSortedCategories);

      try {
        setSortLoading(true);
        await axios.post(
          getApiUrl(
            `/api/protected/restaurant/${outletSlug?.slug}/categories/rearrange_display_order`
          ),
          {
            ordering: newSortedCategories.map((cat, index) => ({
              category_slug: cat.slug,
              display_order: index + 1,
            })),
          }
        );
        // console.log("Order updated successfully");
      } catch (error) {
        console.error("Error updating order:", error);
        setSortedCategories(categories); // Revert UI
      } finally {
        setSortLoading(false);
      }
    }
  };

  // console.log({ sortedCategories });

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={getCategories}
            className="bg-orange text-white px-4 py-2 rounded-md hover:bg-orange/90"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Categories</h1>
        <p className="text-gray-600">Manage your restaurant categories</p>
      </div>

      <div className="mb-2 flex items-center gap-3">
        <div className="grow">
          <InputField
            id="search"
            name="search"
            type="text"
            placeholder="Search categories..."
            className="bg-white border border-[#E6E6E6] rounded-lg"
            icon={<Icon name="Search" width={20} height={20} />}
            containerClassName="max-w-[700px]"
          />
        </div>
        <div className="flex items-center gap-4">
          <CustomButton
            label="Add Category"
            icon={<Icon name="Plus" width={16} height={16} />}
            bgColor="bg-[var(--color-orange)]"
            textColor="text-white"
            className="px-4 py-2"
            onClick={() => (window.location.href = "/categories/add")}
          />
        </div>
      </div>

      {/* <div className="md:hidden">
        <MobileCategory categories={categories} />
      </div>

      <div className="hidden md:block">
        <CategoryDataTable categories={categories} />
      </div> */}

      <div className="relative">
        {/* Loading Overlay */}
        {sortLoading && (
          <div
            className="absolute inset-0 bg-opacity-30 flex items-center justify-center z-50 rounded-lg"
            style={{ backgroundColor: "rgba(0, 0, 0, 0.3)" }}
          >
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange mx-auto mb-2"></div>
              <p className="text-white text-sm">Updating order...</p>
            </div>
          </div>
        )}

        <DndContext
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={sortedCategories?.map((item) => item.slug)}
            strategy={verticalListSortingStrategy}
          >
            {sortedCategories?.map((item) => {
              // console.log({ item });
              return (
                <SortableItem key={item?.slug} id={item?.slug}>
                  <div>
                    <Link to={`${item?.slug}`}>
                      <h3>{item.name}</h3>
                      {/* <p>{item.description}</p> */}
                      {/* Or any other content */}
                    </Link>
                  </div>
                </SortableItem>
              );
            })}
          </SortableContext>
        </DndContext>
      </div>
    </div>
  );
};

// export default withAuthProtection(CategoriesListPage);
export default CategoriesListPage;
