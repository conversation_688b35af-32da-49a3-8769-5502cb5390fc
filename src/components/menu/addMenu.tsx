import React, { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import type { SubmitHandler } from "react-hook-form";
import axios from "axios";
import toast from "react-hot-toast";
import InputField from "../common/InputField";
import CustomButton from "../common/CustomButton";
import { getApiUrl } from "../../utils/api";
import { useApi } from "../../context/ApiContext";
import { useAuth } from "../../context/AuthContext";

// Interface for menu form data
interface MenuFormData {
  name: string;
  category_slug: string; // This will store the category slug
  desc: string;
  price: number;
  image: FileList;
}

const AddMenu: React.FC = () => {
  const { categories } = useApi();
  const { loading, setLoading, outletSlug } = useAuth();
  const [isToggled, setIsToggled] = useState<boolean>(false);
  const [descLoading, setDescLoading] = useState<boolean>(false);
  const [improvedDescription, setImprovedDescription] = useState<string>("");

  const handleToggle = () => {
    setIsToggled(!isToggled);
  };

  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    formState: { errors },
    reset,
    control,
  } = useForm<MenuFormData>({
    mode: "onSubmit", // best for this case
  });

  // Handle form submission
  // const handleAddMenu: SubmitHandler<MenuFormData> = async (data) => {
  //   console.log({ data });
  //   const formData = new FormData();

  //   formData.append("name", data.name);
  //   formData.append("category_slug", data.category_slug);
  //   formData.append("description", data.desc); // Note: description, not desc
  //   formData.append("price", data.price.toString());
  //   formData.append("is_available", "true");
  //   if (isToggled) {
  //     formData.append("image", data.image[0]);
  //   }
  //   console.log("FormData contents:");
  //   for (let [key, value] of formData.entries()) {
  //     console.log(key, value);
  //   }

  //   const apiUrl = getApiUrl(
  //     `/api/protected/restaurant/${outletSlug?.slug}/items${isToggled ? "" : "/no-image"}`
  //   );
  //   console.log(apiUrl, "apiUrl");

  //   try {
  //     setLoading(true);
  //     await axios.post(apiUrl, formData, {
  //       withCredentials: true,
  //     });

  //     toast.success("Menu item added successfully!");
  //     reset(); // Reset form after successful submission
  //   } catch (error) {
  //     console.error("Error adding menu item:", error);
  //     toast.error("Failed to add menu item");
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const typeAIText = async (text: string, delay = 20) => {
    let current = "";
    for (let i = 0; i < text.length; i++) {
      current += text[i];
      setValue("desc", current);
      await new Promise((r) => setTimeout(r, delay));
    }
  };

  const handleImproveAI = async () => {
    const getDesc = getValues("desc");
    const getName = getValues("name");

    if (!getDesc || !getDesc) {
      toast.error("Please fill out name and description first.");
      return;
    }

    const params = new URLSearchParams({
      item_name: getDesc,
      description: getDesc,
    });

    try {
      setDescLoading(true);
      console.log("called");
      const res = await axios.get(
        getApiUrl(
          `/api/protected/restaurant/${outletSlug?.slug}/items/enhance_description_with_ai?${params}`
        )
      );
      setImprovedDescription(res?.data?.data);
      console.log({ res });

      if (res.status === 200 && res?.data?.data) {
        const improved = res.data.data;
        await typeAIText(improved); // ✅ directly set into textarea
        setImprovedDescription(improved); // optional: store in state if needed elsewhere
        toast.success("Description improved!");
      } else {
        toast.error("Could not improve the description.");
      }
    } catch (e) {
      toast.error("Failed to improve AI");
      console.log(e, "error");
    } finally {
      setDescLoading(false);
    }

    console.log({ getDesc, getName });
  };

  console.log({ improvedDescription });

  const handleAddMenu: SubmitHandler<MenuFormData> = async (data) => {
    console.log({ data });

    const apiUrl = getApiUrl(
      `/api/protected/restaurant/${outletSlug?.slug}/items${isToggled ? "" : "/no-image"}`
    );
    console.log(apiUrl, "apiUrl");

    try {
      setLoading(true);

      if (isToggled) {
        // Send multipart/form-data
        const formData = new FormData();
        formData.append("name", data.name);
        formData.append("category_slug", data.category_slug);
        formData.append(
          "description",
          improvedDescription ? improvedDescription : data.desc
        ); // 'desc' → 'description'
        formData.append("price", data.price.toString());
        formData.append("image", data.image[0]);

        await axios.post(apiUrl, formData, {
          withCredentials: true,
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });
      } else {
        // Send plain JSON
        const jsonData = {
          name: data.name,
          category_slug: data.category_slug,
          description: improvedDescription ? improvedDescription : data.desc,
          price: data.price,
        };

        await axios.post(apiUrl, jsonData, {
          withCredentials: true,
          headers: {
            "Content-Type": "application/json",
          },
        });
      }

      toast.success("Menu item added successfully!");
      reset(); // Reset form after successful submission
    } catch (error) {
      console.error("Error adding menu item:", error);
      toast.error("Failed to add menu item");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // getCategories();
  }, []);

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">
        Add New Menu Item
      </h2>

      <form onSubmit={handleSubmit(handleAddMenu)} className="space-y-6">
        {/* Name Field */}
        <div>
          <InputField
            id="name"
            type="text"
            label="Menu Item Name *"
            placeholder="Enter menu item name"
            {...register("name", {
              required: "Menu item name is required",
              minLength: {
                value: 2,
                message: "Name must be at least 2 characters long",
              },
            })}
            error={errors.name?.message}
          />
        </div>

        {/* Category Dropdown */}
        <div>
          <label htmlFor="category" className="block text-sm font-medium mb-1">
            Category *
          </label>
          <select
            id="category"
            {...register("category_slug", {
              required: "Please select a category",
            })}
            className={`w-full rounded-md border border-[#E6E6E6] px-3 py-2 text-sm outline-none focus:border-[var(--color-orange)] transition-all duration-200 ${
              errors.category_slug ? "border-red-500" : ""
            }`}
            disabled={loading}
          >
            <option value="">
              {loading ? "Loading categories..." : "Select a category"}
            </option>
            {categories?.map((category) => (
              <option key={category.slug} value={category.slug || category.id}>
                {category.name}
              </option>
            ))}
          </select>
          {errors.category_slug && (
            <p className="mt-1 text-xs text-red-500">
              {errors.category_slug.message}
            </p>
          )}
        </div>

        {/* Description Field */}
        <div className="relative">
          <label htmlFor="desc" className="block text-sm font-medium mb-1">
            Description *
          </label>

          <Controller
            name="desc"
            control={control}
            rules={{
              required: "Description is required",
              minLength: {
                value: 10,
                message: "Description must be at least 10 characters long",
              },
            }}
            render={({ field }) => (
              <textarea
                {...field}
                id="desc"
                rows={4}
                disabled={descLoading}
                placeholder={
                  descLoading
                    ? "Improving description..."
                    : "Enter menu item description"
                }
                className={`w-full rounded-md border border-[#E6E6E6] px-3 py-2 pr-28 text-sm outline-none transition-all duration-200 resize-vertical ${
                  errors.desc
                    ? "border-red-500"
                    : "focus:border-[var(--color-orange)]"
                } ${descLoading ? "bg-gray-100 text-gray-500 cursor-wait" : ""}`}
              />
            )}
          />

          <button
            type="button"
            onClick={handleImproveAI}
            disabled={descLoading}
            className={`absolute bottom-4 right-2 text-xs px-3 py-1.5 rounded-md shadow transition ${
              descLoading
                ? "bg-[var(--color-orange-border)] text-white cursor-wait"
                : "bg-[var(--color-orange)] text-white hover:bg-[var(--color-orange-border)]"
            }`}
          >
            {descLoading ? "Improving..." : "Improve with AI"}
          </button>

          {errors.desc && (
            <p className="mt-1 text-xs text-red-500">{errors.desc.message}</p>
          )}
        </div>

        {/* Price Field */}
        <div>
          <InputField
            id="price"
            type="number"
            label="Price *"
            placeholder="Enter price"
            step="0.01"
            min="0"
            {...register("price", {
              required: "Price is required",
              min: {
                value: 0.01,
                message: "Price must be greater than 0",
              },
              valueAsNumber: true,
            })}
            error={errors.price?.message}
          />
        </div>

        <div className="flex items-center space-x-4">
          <button
            type="button"
            onClick={handleToggle}
            className={`
              relative inline-flex items-center w-11 h-6 rounded-full transition-colors duration-200 ease-in-out
              focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500
              ${isToggled ? "bg-[#ea6a12]" : "bg-gray-200"}
            `}
            style={{
              backgroundColor: isToggled ? "#ea6a12" : "#e5e7eb",
            }}
          >
            <span
              className={`
                inline-block w-5 h-5 bg-white rounded-full shadow-lg transform transition-transform duration-200 ease-in-out
                ${isToggled ? "translate-x-5" : "translate-x-0.5"}
              `}
            />
          </button>
        </div>

        <div className="mb-2">
          <label className="block text-sm font-medium text-gray-700">
            {isToggled ? "Upload Custom Image" : "AI-Generated Image"}
          </label>
          <p className="text-xs text-gray-500 mt-1">
            {isToggled
              ? "Upload your own image file (JPEG, PNG max 5MB)"
              : "An image will be automatically generated using AI"}
          </p>
        </div>

        {isToggled && (
          <div>
            <InputField
              id="image"
              // label="Menu Item Image *"
              type="file"
              accept="image/*"
              {...register("image", {
                required: "Image is required",
                validate: {
                  fileType: (files: FileList) => {
                    if (!files || files.length === 0) return true;
                    const file = files[0];
                    const allowedTypes = [
                      "image/jpeg",
                      "image/jpg",
                      "image/png",
                      "image/webp",
                      "image/gif",
                    ];
                    return (
                      allowedTypes.includes(file.type) ||
                      "Please select a valid image file (JPEG, PNG, WebP, GIF)"
                    );
                  },
                  fileSize: (files: FileList) => {
                    if (!files || files.length === 0) return true;
                    const file = files[0];
                    const maxSize = 5 * 1024 * 1024; // 5MB
                    return (
                      file.size <= maxSize || "File size must be less than 5MB"
                    );
                  },
                },
              })}
              error={errors.image?.message}
            />
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <CustomButton
            label="Cancel"
            onClick={() => reset()}
            bgColor="bg-gray-500"
            textColor="text-white"
            className="px-6 py-2"
          />
          <CustomButton
            label={loading ? "Adding..." : "Add Menu Item"}
            bgColor="bg-[var(--color-orange)]"
            textColor="text-white"
            className="px-6 py-2"
            disabled={loading || loading}
          />
        </div>
      </form>
    </div>
  );
};

export default AddMenu;
