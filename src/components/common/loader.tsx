const Loader = () => {
  return (
    <div
      className="absolute inset-0 z-50 flex justify-center items-center -ml-[10px]"
      style={{ backgroundColor: "rgba(0, 0, 0, 0.3)" }}
    >
      <div className="rotate-loader">
        <svg
          width="80"
          height="80"
          viewBox="0 0 134 134"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M128.482 66.9999C128.482 33.0442 100.956 5.51764 66.9999 5.51764C33.0442 5.51764 5.51764 33.0442 5.51764 66.9999C5.51764 100.956 33.0442 128.482 66.9999 128.482V134C29.9969 134 0 104.003 0 66.9999C0 29.9969 29.9969 0 66.9999 0C104.003 0 134 29.9969 134 66.9999C134 104.003 104.003 134 66.9999 134V128.482C100.956 128.482 128.482 100.956 128.482 66.9999Z"
            fill="#F66D1C"
          />
          <path
            d="M43.3569 86.1099C34.1027 95.4634 28.5519 100.75 18.5168 109.666C20.3227 112.274 21.611 113.59 24.3745 115.485C33.8847 106.119 39.1639 100.628 48.2821 91.0542C51.353 87.8299 53.4214 85.6802 59.6315 86.1099C71.4091 86.1099 78.6899 82.4556 86.8271 71.9217C95.1786 59.4535 94.132 50.2097 89.1827 44.6201C82.3302 36.8814 70.7667 39.0312 60.2739 47.2C51.0659 55.1541 47.6397 61.3882 48.2821 74.9315C48.2232 79.7037 47.5627 82.1725 43.3569 86.1099Z"
            fill="#F66D1C"
          />
          <path
            d="M48.8792 30.3414C23.8499 43.1502 22.6673 68.5727 29.9587 84.5606C32.9113 88.7093 28.379 92.7362 25.8204 88.3047C15.3387 70.1492 20.8945 47.8816 33.5086 34.6766C55.3792 11.4255 92.6107 18.0997 107.012 43.1501C124.232 73.105 104.998 104.047 81.194 111.137C62.6705 116.655 52.4234 111.531 45.9205 108.434C40.994 105.59 44.5416 101.876 49.6646 104.296C59.1234 108.181 68.2607 109.245 79.2234 106.211C101.294 100.102 117.65 70.7403 101.888 45.7118C90.4021 27.4732 67.8639 20.6258 48.8792 30.3414Z"
            fill="#F66D1C"
          />
        </svg>
      </div>
    </div>
  );
};

export default Loader;
