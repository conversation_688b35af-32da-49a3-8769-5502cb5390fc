// SortableList.tsx
import { DndContext, closestCenter } from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useState, useEffect } from "react";
import SortableItem from "./SortableItem";
import { useApi } from "../../context/ApiContext";
import axios from "axios";
import { getApiUrl } from "../../utils/api";
import { useAuth } from "../../context/AuthContext";

export default function SortableList() {
  const { categories } = useApi();
  const { outletSlug } = useAuth();
  const [sortLoading, setSortLoading] = useState(false);
  const [sortedCategories, setSortedCategories] = useState(categories || []);

  // Update local state when categories change
  useEffect(() => {
    setSortedCategories(categories || []);
  }, [categories]);

  const handleDragEnd = async (event) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      let newDisplayOrder;

      // Update the order in state
      setSortedCategories((prevCategories) => {
        const oldIndex = prevCategories.findIndex(
          (cat) => cat.slug === active.id
        );
        const newIndex = prevCategories.findIndex(
          (cat) => cat.slug === over.id
        );
        // console.log({ newIndex });
        newDisplayOrder = newIndex + 1;
        return arrayMove(prevCategories, oldIndex, newIndex);
      });

      // console.log({ newDisplayOrder });

      // Send to API (fire and forget, or handle errors)
      try {
        setSortLoading(true);
        await axios.post(
          getApiUrl(
            `/api/protected/restaurant/${outletSlug?.slug}/categories/rearrange_display_order`
          ),
          {
            ordering: [
              {
                category_slug: active.id,
                display_order: newDisplayOrder,
              },
            ],
          }
        );

        // API call successful, UI is already correct
      } catch (error) {
        console.error("Error updating order:", error);
        // Revert to original order on error
        setSortedCategories(categories);
      } finally {
        setSortLoading(false);
      }
    }
  };

  useEffect(() => {
    if (outletSlug?.slug) {
      // rearrangeCategories();
    }
  }, []);

  return (
    <div className="relative">
      {/* Loading Overlay */}
      {sortLoading && (
        <div
          className="absolute inset-0 bg-opacity-30 flex items-center justify-center z-50 rounded-lg"
          style={{ backgroundColor: "rgba(0, 0, 0, 0.3)" }}
        >
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange mx-auto mb-2"></div>
            <p className="text-white text-sm">Updating order...</p>
          </div>
        </div>
      )}

      <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <SortableContext
          items={sortedCategories?.map((item) => item.slug)}
          strategy={verticalListSortingStrategy}
        >
          {sortedCategories?.map((item) => {
            // console.log({ item });
            return (
              <SortableItem key={item?.slug} id={item?.slug}>
                <div>
                  <h3>{item.name}</h3>
                  {/* <p>{item.description}</p> */}
                  {/* Or any other content */}
                </div>
              </SortableItem>
            );
          })}
        </SortableContext>
      </DndContext>
    </div>
  );
}
