// SortableItem.tsx
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import type { ReactNode } from 'react';

interface SortableItemProps {
  id: string;
  children: ReactNode;
}

export default function SortableItem({ id, children  }: SortableItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
    padding: '12px 16px',
    margin: '8px 0',
    border: '1px solid #ddd',
    borderRadius: 6,
    backgroundColor: isDragging ? '#e0f2fe' : '#fff',
    cursor: 'grab',
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {children}
    </div>
  );
}
