import axios from "axios";
import React, {
  createContext,
  useEffect,
  useState,
  type ReactNode,
} from "react";
import { getApiUrl } from "../utils/api";
import { useAuth } from "./AuthContext";

interface Category {
  id?: string;
  name: string;
  description: string;
  outlet?: string;
  slug?: string;
  image?: string;
}

interface ApiDataProviderContextType {
  categories: Category[];
  setCategories: React.Dispatch<React.SetStateAction<Category[]>>;
  getCategories: () => void;
  userSlug: string | null;
  setUserSlug: React.Dispatch<React.SetStateAction<string | null>>;
}

export const ApiContext = createContext<ApiDataProviderContextType | undefined>(
  undefined
);

export const ApiDataProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const { outletSlug, setLoading } = useAuth(); // Added outletSlug to the function
  const [categories, setCategories] = useState<Category[]>([]);
  const [userSlug, setUserSlug] = useState<string | null>(null);
  // console.log("ApiDataProvider");

  const getCategories = async () => {
    try {
      setLoading(true);
      const res = await axios.get(
        getApiUrl(
          `/api/protected/restaurant/${outletSlug?.slug}/categories?slug=__all__`
        ),
        {
          withCredentials: true,
          // headers: {
          //   slug: "__all__",
          // },
        }
      );
      console.log({ res });
      setCategories(res?.data?.data?.categories);
    } catch (error) {
      console.error("Error fetching categories:", error);
    } finally {
      setLoading(false);
    }
  };

  const getOutlets = async () => {
    try {
      const res = await axios.get(getApiUrl(`/api/open/`));
      setUserSlug(res?.data?.data?.outlets[0]);
    } catch (e) {
      console.log({ e });
    }
  };

  useEffect(() => {
    if (outletSlug?.slug) {
      getCategories();
    }
    getOutlets()
  }, [outletSlug?.slug]);

  return (
    <ApiContext.Provider
      value={{
        categories,
        setCategories,
        getCategories,
        userSlug,
        setUserSlug,
      }}
    >
      {children}
    </ApiContext.Provider>
  );
};

// Custom hook to use the AuthContext
export const useApi = () => {
  // Changed from useAuth to useApi
  const context = React.useContext(ApiContext);
  if (context === undefined) {
    throw new Error("useApi must be used within an ApiDataProvider"); // Updated error message
  }
  return context;
};
