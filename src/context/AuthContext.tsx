import axios from "axios";
import React, {
  createContext,
  useEffect,
  useState,
  useCallback,
  type ReactNode,
} from "react";
import { getApiUrl } from "../utils/api";

interface OutletInfo {
  name: string;
  slug: string;
}

interface UserInfo {
  email: string;
  name: string | null;
  ph_no: string | null;
  role: string;
  slug: string;
  extras: {
    outlet: OutletInfo;
  };
}

// Define the context type
interface AuthContextType {
  userInfo: UserInfo | null;
  setUserInfo: (userInfo: UserInfo | null) => void;
  loading: boolean;
  setLoading: (loading: boolean) => void;
  outletSlug: OutletInfo | null | undefined;
  setOutletSlug: (outletSlug: OutletInfo | null | undefined) => void;
  user: boolean;
  setUser: (user: boolean) => void;
  authLoading: boolean;
  setAuthLoading: (authLoading: boolean) => void;
  isAuth: boolean;
  setIsAuth: (isAuth: boolean) => void;
  refreshAuth: () => Promise<void>;
  // logout: () => void;
}

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<boolean>(false);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [authLoading, setAuthLoading] = useState<boolean>(true); // Start with true
  const [isAuth, setIsAuth] = useState<boolean>(false);
  const [outletSlug, setOutletSlug] = useState<OutletInfo | null | undefined>(
    null
  );

  const getUserInfo = useCallback(async () => {
    try {
      setAuthLoading(true);
      const res = await axios.get(getApiUrl("/api/protected/auth/user-info"), {
        withCredentials: true,
      });
      
      const userData = res.data.data;
      setUserInfo(userData);
      setIsAuth(true);
      setOutletSlug(userData?.extras?.outlet);
    } catch (error) {
      console.log(error, "error from auth check");
      // Clear auth state on error
      setUserInfo(null);
      setIsAuth(false);
      setOutletSlug(null);
    } finally {
      setAuthLoading(false);
    }
  }, []);

  // const logout = useCallback(() => {
  //   setUserInfo(null);
  //   setIsAuth(false);
  //   setOutletSlug(null);
  //   // You might want to also call a logout API endpoint here
  // }, []);

  // Initialize auth check on mount and when user state changes
  useEffect(() => {
    getUserInfo();
    console.log("re trigger");
  }, [getUserInfo, user]);

  // Update outlet slug when user info changes
  useEffect(() => {
    if (userInfo) {
      setOutletSlug(userInfo?.extras?.outlet);
    }
  }, [userInfo]);

  console.log({userInfo})

  const value: AuthContextType = {
    userInfo,
    setUserInfo,
    loading,
    setLoading,
    outletSlug,
    setOutletSlug,
    user,
    setUser,
    authLoading,
    setAuthLoading,
    isAuth,
    setIsAuth,
    refreshAuth: getUserInfo,
    // logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the AuthContext
export const useAuth = () => {
  const context = React.useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};